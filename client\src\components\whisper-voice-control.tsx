import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Mi<PERSON>, MicOff, Square } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useWhisperVoice } from '@/hooks/use-whisper-voice';

interface WhisperVoiceControlProps {
  className?: string;
  enableTextToSpeech?: boolean;
}

const WhisperVoiceControl: React.FC<WhisperVoiceControlProps> = ({
  className,
  enableTextToSpeech = true
}) => {
  const {
    isRecording,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    aiResponse,
    error,
    startRecording,
    stopRecording,
    checkMicrophonePermission
  } = useWhisperVoice({
    enableTextToSpeech
  });

  // Show disabled state if not supported
  if (!isSupported) {
    return (
      <div className={cn("relative", className)}>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          disabled={true}
          className="relative overflow-hidden opacity-50"
          aria-label="Audio recording not supported"
          title="Audio recording not supported in this browser"
        >
          <MicOff className="h-4 w-4 text-gray-400" />
        </Button>
      </div>
    );
  }

  const handleToggleRecording = () => {
    console.log('🎯 handleToggleRecording called', { isRecording, hasPermission });

    if (isRecording) {
      console.log('🛑 Stopping recording...');
      stopRecording();
    } else {
      console.log('🎤 Starting recording...');
      startRecording();
    }
  };

  return (
    <div className={cn("relative", className)}>
      <Button
        type="button"
        variant="ghost"
        size="lg"
        onClick={handleToggleRecording}
        disabled={!isSupported || isProcessing}
        className={cn(
          "relative overflow-hidden transition-all duration-300 w-12 h-12 rounded-full",
          isRecording
            ? "bg-red-500 hover:bg-red-600 text-white"
            : isProcessing
            ? "bg-blue-500 hover:bg-blue-600 text-white"
            : hasPermission === false
            ? "bg-orange-500 hover:bg-orange-600 text-white"
            : hasPermission === null
            ? "bg-blue-500 hover:bg-blue-600 text-white"
            : "bg-blue-500 hover:bg-blue-600 text-white"
        )}
        aria-label={isRecording ? "Stop recording" : isProcessing ? "Processing..." : "Start voice recording"}
        title={isRecording ? "Click to stop recording" : isProcessing ? "Processing your voice..." : "Click to start voice recording"}
      >
        {isRecording ? (
          <Square className="h-6 w-6" />
        ) : isProcessing ? (
          <div className="h-6 w-6 animate-spin border-2 border-current border-t-transparent rounded-full" />
        ) : (
          <Mic className="h-6 w-6" />
        )}

        {/* Recording pulse animation */}
        {isRecording && (
          <div className="absolute inset-0 rounded-full bg-red-500/20 animate-ping"></div>
        )}

        {/* Processing brain wave effect */}
        {isProcessing && (
          <div className="absolute inset-0 rounded-full">
            <div className="absolute inset-1 rounded-full bg-purple-500/10 animate-pulse"></div>
            <div className="absolute inset-2 rounded-full bg-purple-500/20 animate-ping"></div>
          </div>
        )}

        {/* Permission indicator dot */}
        {(hasPermission === null || hasPermission === false) && !isRecording && !isProcessing && (
          <div className={cn(
            "absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse",
            hasPermission === null ? "bg-blue-500" : "bg-orange-500"
          )}></div>
        )}
      </Button>

      {/* Voice feedback display - now handled by search interface */}
      {false && (isRecording || isProcessing || transcript || aiResponse || error) && (
        <div className="fixed top-[280px] left-1/2 transform -translate-x-1/2 w-80 max-w-sm bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg p-3 shadow-xl z-[9999]">
          {/* Status indicator */}
          <div className="flex items-center gap-2 mb-2">
            <div className={cn(
              "w-2 h-2 rounded-full",
              isRecording ? "bg-red-500 animate-pulse" :
              isProcessing ? "bg-purple-500 animate-pulse" :
              "bg-green-500"
            )}></div>
            <span className="text-xs font-mono text-gray-400">
              {isRecording ? "Recording..." :
               isProcessing ? "Processing with Whisper..." :
               "Ready"}
            </span>
          </div>

          {/* Transcript display */}
          {transcript && (
            <div className="mb-2">
              <p className="text-xs text-gray-500 font-mono mb-1">You said:</p>
              <p className="text-sm text-white font-mono bg-gray-800 rounded p-2">
                "{transcript}"
              </p>
            </div>
          )}

          {/* AI response display */}
          {aiResponse && (
            <div className="mb-2">
              <p className="text-xs text-gray-500 font-mono mb-1">DasWos AI:</p>
              <p className="text-sm text-blue-300 font-mono bg-gray-800 rounded p-2">
                {typeof aiResponse === 'string' ? aiResponse : aiResponse.response || 'Processing...'}
              </p>
              {typeof aiResponse === 'object' && aiResponse.intent && (
                <p className="text-xs text-gray-400 font-mono mt-1">
                  Intent: {aiResponse.intent} (confidence: {Math.round((aiResponse.confidence || 0) * 100)}%)
                </p>
              )}
            </div>
          )}

          {/* Error display */}
          {error && (
            <div className="mb-2">
              <p className="text-xs text-red-400 font-mono mb-1">Error:</p>
              <p className="text-sm text-red-300 font-mono bg-red-900/20 rounded p-2">
                {error}
              </p>
            </div>
          )}

          {/* Permission help */}
          {(hasPermission === false || hasPermission === null) && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-blue-400 font-mono mb-1">
                {hasPermission === null ? "Voice Commands Available:" : "Microphone Access Required:"}
              </p>
              <p className="text-xs text-gray-400 font-mono">
                {hasPermission === null
                  ? "Click the microphone button to enable voice commands and start talking to DasWos AI."
                  : "Click the microphone button to allow access and start using voice commands."
                }
              </p>
            </div>
          )}

          {/* Voice commands help */}
          {isRecording && hasPermission === true && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-gray-500 font-mono mb-1">Try saying:</p>
              <ul className="text-xs text-gray-400 font-mono space-y-0.5">
                <li>• "Search for wireless headphones"</li>
                <li>• "Go to my profile"</li>
                <li>• "Start AutoShop"</li>
                <li>• "What can you do?"</li>
              </ul>
            </div>
          )}

          {/* Processing help */}
          {isProcessing && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-purple-400 font-mono mb-1">Processing Audio:</p>
              <p className="text-xs text-gray-400 font-mono">
                Using OpenAI Whisper to transcribe your speech and GPT to understand your command...
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WhisperVoiceControl;
