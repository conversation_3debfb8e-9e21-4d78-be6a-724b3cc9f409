import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Mi<PERSON>, MicOff, Square } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useWhisperVoice } from '@/hooks/use-whisper-voice';

interface WhisperVoiceControlProps {
  className?: string;
  enableTextToSpeech?: boolean;
  isAiModeEnabled?: boolean;
}

const WhisperVoiceControl: React.FC<WhisperVoiceControlProps> = ({
  className,
  enableTextToSpeech = true,
  isAiModeEnabled = false
}) => {
  const [showAnimation, setShowAnimation] = useState(false);
  const [hasPlayedAnimation, setHasPlayedAnimation] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const {
    isRecording,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    aiResponse,
    error,
    startRecording,
    stopRecording,
    checkMicrophonePermission
  } = useWhisperVoice({
    enableTextToSpeech
  });

  // Play animation when AI mode is first enabled
  useEffect(() => {
    console.log('🎬 Voice animation effect:', { isAiModeEnabled, hasPlayedAnimation, showAnimation });
    if (isAiModeEnabled && !hasPlayedAnimation) {
      console.log('🎬 Starting voice animation');
      setShowAnimation(true);
      setHasPlayedAnimation(true);

      // Fallback timeout - switch to static button after 3 seconds
      const fallbackTimeout = setTimeout(() => {
        console.log('🎬 Animation timeout, switching to static button');
        setShowAnimation(false);
      }, 3000);

      if (videoRef.current) {
        console.log('🎬 Playing video');
        videoRef.current.play().catch((error) => {
          console.error('🎬 Video play error:', error);
          clearTimeout(fallbackTimeout);
          setShowAnimation(false);
        });
      } else {
        console.log('🎬 Video ref not available');
      }

      return () => clearTimeout(fallbackTimeout);
    }
  }, [isAiModeEnabled, hasPlayedAnimation]);

  // Handle video end to switch to static button
  const handleVideoEnd = () => {
    console.log('🎬 Video ended, switching to static button');
    setShowAnimation(false);
  };

  // Handle video error - fallback to static button
  const handleVideoError = () => {
    console.log('🎬 Video failed to load, switching to static button');
    setShowAnimation(false);
  };

  // Show disabled state if not supported
  if (!isSupported) {
    return (
      <div className={cn("relative", className)}>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          disabled={true}
          className="relative overflow-hidden opacity-50"
          aria-label="Audio recording not supported"
          title="Audio recording not supported in this browser"
        >
          <MicOff className="h-4 w-4 text-gray-400" />
        </Button>
      </div>
    );
  }

  const handleToggleRecording = () => {
    console.log('🎯 handleToggleRecording called', { isRecording, hasPermission });

    if (isRecording) {
      console.log('🛑 Stopping recording...');
      stopRecording();
    } else {
      console.log('🎤 Starting recording...');
      startRecording();
    }
  };

  return (
    <div className={cn("relative", className)}>
      {showAnimation ? (
        // Video animation when AI is first enabled
        <div
          className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 flex items-center justify-center animate-pulse cursor-pointer"
          onClick={handleToggleRecording}
          aria-label={isRecording ? "Stop recording" : isProcessing ? "Processing..." : "Start voice recording"}
          title={isRecording ? "Click to stop recording" : isProcessing ? "Processing your voice..." : "Click to start voice recording"}
        >
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            onEnded={handleVideoEnd}
            onLoadStart={() => console.log('🎬 Video loading started')}
            onCanPlay={() => console.log('🎬 Video can play')}
            onError={handleVideoError}
            muted
            playsInline
            autoPlay
          >
            <source src="/assets/voice-icon.mp4" type="video/mp4" />
          </video>
          {/* Fallback animation if video fails */}
          <div className="absolute inset-0 flex items-center justify-center">
            <Mic className="h-6 w-6 text-white animate-bounce" />
          </div>
        </div>
      ) : (
        // New permanent voice button with animated border when listening
        <div className="relative">
          {/* Animated border when listening */}
          {isRecording && (
            <div className="absolute -inset-1 rounded-full overflow-hidden">
              {/* Circling gradient border */}
              <div
                className="absolute inset-0 rounded-full animate-spin opacity-75"
                style={{
                  background: 'conic-gradient(from 0deg, #3b82f6, #10b981, #eab308, #3b82f6)',
                  animationDuration: '2s'
                }}
              ></div>

              {/* Twinkle effects */}
              <div className="absolute top-0 left-1/4 w-1 h-1 bg-white rounded-full animate-ping" style={{ animationDelay: '0s' }}></div>
              <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-white rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
              <div className="absolute bottom-0 left-3/4 w-1 h-1 bg-white rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
              <div className="absolute top-1/4 right-1/2 w-1 h-1 bg-white rounded-full animate-ping" style={{ animationDelay: '1.5s' }}></div>
            </div>
          )}

          <div
            className={cn(
              "relative w-12 h-12 rounded-full overflow-hidden flex items-center justify-center cursor-pointer transition-all duration-300 shadow-lg z-10",
              isRecording
                ? "bg-red-500 hover:bg-red-600"
                : "bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border-2 border-gray-300 dark:border-gray-600"
            )}
            onClick={handleToggleRecording}
            aria-label={isRecording ? "Stop recording" : isProcessing ? "Processing..." : "Start voice recording"}
            title={isRecording ? "Click to stop recording" : isProcessing ? "Processing your voice..." : "Click to start voice recording"}
          >
          {/* Voice icon styled like the video icon */}
          {isRecording ? (
            <Square className="h-6 w-6 text-white" />
          ) : isProcessing ? (
            <div className="h-6 w-6 animate-spin border-2 border-gray-600 dark:border-gray-300 border-t-transparent rounded-full" />
          ) : (
            // Microphone icon styled to match the video icon
            <svg className="h-6 w-6 text-gray-700 dark:text-gray-300" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2a3 3 0 0 1 3 3v6a3 3 0 0 1-6 0V5a3 3 0 0 1 3-3Z"/>
              <path d="M19 10v1a7 7 0 0 1-14 0v-1"/>
              <path d="M12 18v4"/>
              <path d="M8 22h8"/>
            </svg>
          )}

          {/* Recording pulse animation */}
          {isRecording && (
            <div className="absolute inset-0 rounded-full bg-red-500/20 animate-ping"></div>
          )}

          {/* Processing brain wave effect */}
          {isProcessing && (
            <div className="absolute inset-0 rounded-full">
              <div className="absolute inset-1 rounded-full bg-purple-500/10 animate-pulse"></div>
              <div className="absolute inset-2 rounded-full bg-purple-500/20 animate-ping"></div>
            </div>
          )}

          {/* Permission indicator dot */}
          {(hasPermission === null || hasPermission === false) && !isRecording && !isProcessing && (
            <div className={cn(
              "absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse",
              hasPermission === null ? "bg-blue-500" : "bg-orange-500"
            )}></div>
          )}
          </div>
        </div>
      )}

      {/* Voice feedback display - now handled by search interface */}
      {false && (isRecording || isProcessing || transcript || aiResponse || error) && (
        <div className="fixed top-[280px] left-1/2 transform -translate-x-1/2 w-80 max-w-sm bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg p-3 shadow-xl z-[9999]">
          {/* Status indicator */}
          <div className="flex items-center gap-2 mb-2">
            <div className={cn(
              "w-2 h-2 rounded-full",
              isRecording ? "bg-red-500 animate-pulse" :
              isProcessing ? "bg-purple-500 animate-pulse" :
              "bg-green-500"
            )}></div>
            <span className="text-xs font-mono text-gray-400">
              {isRecording ? "Recording..." :
               isProcessing ? "Processing with Whisper..." :
               "Ready"}
            </span>
          </div>

          {/* Transcript display */}
          {transcript && (
            <div className="mb-2">
              <p className="text-xs text-gray-500 font-mono mb-1">You said:</p>
              <p className="text-sm text-white font-mono bg-gray-800 rounded p-2">
                "{transcript}"
              </p>
            </div>
          )}

          {/* AI response display */}
          {aiResponse && (
            <div className="mb-2">
              <p className="text-xs text-gray-500 font-mono mb-1">DasWos AI:</p>
              <p className="text-sm text-blue-300 font-mono bg-gray-800 rounded p-2">
                {typeof aiResponse === 'string' ? aiResponse : aiResponse.response || 'Processing...'}
              </p>
              {typeof aiResponse === 'object' && aiResponse.intent && (
                <p className="text-xs text-gray-400 font-mono mt-1">
                  Intent: {aiResponse.intent} (confidence: {Math.round((aiResponse.confidence || 0) * 100)}%)
                </p>
              )}
            </div>
          )}

          {/* Error display */}
          {error && (
            <div className="mb-2">
              <p className="text-xs text-red-400 font-mono mb-1">Error:</p>
              <p className="text-sm text-red-300 font-mono bg-red-900/20 rounded p-2">
                {error}
              </p>
            </div>
          )}

          {/* Permission help */}
          {(hasPermission === false || hasPermission === null) && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-blue-400 font-mono mb-1">
                {hasPermission === null ? "Voice Commands Available:" : "Microphone Access Required:"}
              </p>
              <p className="text-xs text-gray-400 font-mono">
                {hasPermission === null
                  ? "Click the microphone button to enable voice commands and start talking to DasWos AI."
                  : "Click the microphone button to allow access and start using voice commands."
                }
              </p>
            </div>
          )}

          {/* Voice commands help */}
          {isRecording && hasPermission === true && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-gray-500 font-mono mb-1">Try saying:</p>
              <ul className="text-xs text-gray-400 font-mono space-y-0.5">
                <li>• "Search for wireless headphones"</li>
                <li>• "Go to my profile"</li>
                <li>• "Start AutoShop"</li>
                <li>• "What can you do?"</li>
              </ul>
            </div>
          )}

          {/* Processing help */}
          {isProcessing && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-purple-400 font-mono mb-1">Processing Audio:</p>
              <p className="text-xs text-gray-400 font-mono">
                Using OpenAI Whisper to transcribe your speech and GPT to understand your command...
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WhisperVoiceControl;
