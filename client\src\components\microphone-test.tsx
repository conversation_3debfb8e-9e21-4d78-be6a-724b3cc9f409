import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Mic, MicOff } from 'lucide-react';

const MicrophoneTest: React.FC = () => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isRequesting, setIsRequesting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [aiTestResult, setAiTestResult] = useState<string | null>(null);
  const [isTestingAI, setIsTestingAI] = useState(false);
  const [isTestingSpeech, setIsTestingSpeech] = useState(false);
  const [speechTestResult, setSpeechTestResult] = useState<string | null>(null);

  const testMicrophone = async () => {
    console.log('🧪 Testing microphone access...');
    setIsRequesting(true);
    setError(null);

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('MediaDevices API not supported');
      }

      console.log('📱 Requesting microphone access...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      console.log('✅ Microphone access granted!');
      setHasPermission(true);

      // Stop the stream immediately
      stream.getTracks().forEach(track => {
        console.log('🛑 Stopping track:', track.label);
        track.stop();
      });

    } catch (err: any) {
      console.error('❌ Microphone access failed:', err);
      setHasPermission(false);
      setError(err.message || 'Failed to access microphone');
    } finally {
      setIsRequesting(false);
    }
  };

  const testAI = async () => {
    console.log('🤖 Testing AI API...');
    setIsTestingAI(true);
    setAiTestResult(null);

    try {
      const response = await fetch('/api/ai-conversation/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Hello, this is a test message',
          conversationHistory: []
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ AI API response:', data);
      setAiTestResult(`AI Response: ${data.response || 'No response'}`);

    } catch (err: any) {
      console.error('❌ AI API test failed:', err);
      setAiTestResult(`AI Test Failed: ${err.message}`);
    } finally {
      setIsTestingAI(false);
    }
  };

  const testSpeechRecognition = async () => {
    console.log('🗣️ Testing speech recognition...');
    setIsTestingSpeech(true);
    setSpeechTestResult(null);

    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (!SpeechRecognition) {
        throw new Error('Speech recognition not supported in this browser');
      }

      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      let finalTranscript = '';
      let hasDetectedSpeech = false;

      recognition.onstart = () => {
        console.log('🎤 Speech recognition started');
        setSpeechTestResult('Listening... Please speak now!');
      };

      recognition.onresult = (event) => {
        console.log('📝 Speech recognition result:', event);
        let interim = '';
        let final = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            final += transcript;
          } else {
            interim += transcript;
          }
        }

        if (interim) {
          setSpeechTestResult(`Hearing: "${interim}"`);
          hasDetectedSpeech = true;
        }

        if (final) {
          finalTranscript = final;
          setSpeechTestResult(`You said: "${final}"`);
          recognition.stop();
        }
      };

      recognition.onerror = (event) => {
        console.error('❌ Speech recognition error:', event.error);
        setSpeechTestResult(`Error: ${event.error}`);
        setIsTestingSpeech(false);
      };

      recognition.onend = () => {
        console.log('🛑 Speech recognition ended');
        if (!hasDetectedSpeech && !finalTranscript) {
          setSpeechTestResult('No speech detected. Try speaking louder or closer to the microphone.');
        }
        setIsTestingSpeech(false);
      };

      recognition.onsoundstart = () => {
        console.log('🔊 Sound detected');
        setSpeechTestResult('Sound detected! Keep speaking...');
      };

      recognition.onspeechstart = () => {
        console.log('🗣️ Speech detected');
        setSpeechTestResult('Speech detected! Continue speaking...');
        hasDetectedSpeech = true;
      };

      recognition.start();

      // Auto-stop after 10 seconds
      setTimeout(() => {
        if (recognition) {
          recognition.stop();
        }
      }, 10000);

    } catch (err: any) {
      console.error('❌ Speech recognition test failed:', err);
      setSpeechTestResult(`Speech Test Failed: ${err.message}`);
      setIsTestingSpeech(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
      <h3 className="text-lg font-semibold mb-4">Microphone Test</h3>

      <div className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button
            onClick={testMicrophone}
            disabled={isRequesting}
            className="flex items-center gap-2"
          >
            {isRequesting ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                Requesting...
              </>
            ) : (
              <>
                <Mic className="w-4 h-4" />
                Test Microphone
              </>
            )}
          </Button>

          <Button
            onClick={testAI}
            disabled={isTestingAI}
            variant="outline"
            className="flex items-center gap-2"
          >
            {isTestingAI ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full"></div>
                Testing AI...
              </>
            ) : (
              <>
                🤖 Test AI API
              </>
            )}
          </Button>

          <Button
            onClick={testSpeechRecognition}
            disabled={isTestingSpeech}
            variant="secondary"
            className="flex items-center gap-2"
          >
            {isTestingSpeech ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full"></div>
                Listening...
              </>
            ) : (
              <>
                🗣️ Test Speech
              </>
            )}
          </Button>
        </div>

        {hasPermission === true && (
          <div className="text-green-600 dark:text-green-400 flex items-center gap-2">
            <Mic className="w-4 h-4" />
            Microphone access granted!
          </div>
        )}

        {hasPermission === false && (
          <div className="text-red-600 dark:text-red-400 flex items-center gap-2">
            <MicOff className="w-4 h-4" />
            Microphone access denied
          </div>
        )}

        {error && (
          <div className="text-red-600 dark:text-red-400 text-sm">
            Error: {error}
          </div>
        )}

        {aiTestResult && (
          <div className={`text-sm ${aiTestResult.includes('Failed') ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
            {aiTestResult}
          </div>
        )}

        {speechTestResult && (
          <div className={`text-sm ${speechTestResult.includes('Failed') || speechTestResult.includes('Error') ? 'text-red-600 dark:text-red-400' : 'text-blue-600 dark:text-blue-400'}`}>
            Speech Test: {speechTestResult}
          </div>
        )}

        <div className="text-sm text-gray-600 dark:text-gray-400">
          <p><strong>Instructions:</strong></p>
          <ol className="list-decimal list-inside space-y-1 mt-2">
            <li>Click "Test Microphone" to check microphone permissions</li>
            <li>Click "Test AI API" to verify OpenAI connection</li>
            <li>Click "Test Speech" to test speech recognition</li>
            <li>If all work, try the main voice button</li>
            <li>Check browser console for detailed logs</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default MicrophoneTest;
