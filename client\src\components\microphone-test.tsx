import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Mic, MicOff } from 'lucide-react';

const MicrophoneTest: React.FC = () => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isRequesting, setIsRequesting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testMicrophone = async () => {
    console.log('🧪 Testing microphone access...');
    setIsRequesting(true);
    setError(null);

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('MediaDevices API not supported');
      }

      console.log('📱 Requesting microphone access...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      console.log('✅ Microphone access granted!');
      setHasPermission(true);
      
      // Stop the stream immediately
      stream.getTracks().forEach(track => {
        console.log('🛑 Stopping track:', track.label);
        track.stop();
      });
      
    } catch (err: any) {
      console.error('❌ Microphone access failed:', err);
      setHasPermission(false);
      setError(err.message || 'Failed to access microphone');
    } finally {
      setIsRequesting(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
      <h3 className="text-lg font-semibold mb-4">Microphone Test</h3>
      
      <div className="space-y-4">
        <Button 
          onClick={testMicrophone} 
          disabled={isRequesting}
          className="flex items-center gap-2"
        >
          {isRequesting ? (
            <>
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
              Requesting...
            </>
          ) : (
            <>
              <Mic className="w-4 h-4" />
              Test Microphone
            </>
          )}
        </Button>

        {hasPermission === true && (
          <div className="text-green-600 dark:text-green-400 flex items-center gap-2">
            <Mic className="w-4 h-4" />
            Microphone access granted!
          </div>
        )}

        {hasPermission === false && (
          <div className="text-red-600 dark:text-red-400 flex items-center gap-2">
            <MicOff className="w-4 h-4" />
            Microphone access denied
          </div>
        )}

        {error && (
          <div className="text-red-600 dark:text-red-400 text-sm">
            Error: {error}
          </div>
        )}

        <div className="text-sm text-gray-600 dark:text-gray-400">
          <p><strong>Instructions:</strong></p>
          <ol className="list-decimal list-inside space-y-1 mt-2">
            <li>Click "Test Microphone" button</li>
            <li>Your browser should show a permission dialog</li>
            <li>Click "Allow" to grant microphone access</li>
            <li>You should see a success message</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default MicrophoneTest;
