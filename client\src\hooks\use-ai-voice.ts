import { useState, useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import { aiConversationService, AIResponse } from '@/services/ai-conversation';

interface AIVoiceOptions {
  continuous?: boolean;
  interimResults?: boolean;
  language?: string;
  enableTextToSpeech?: boolean;
}

export const useAIVoice = (options: AIVoiceOptions = {}) => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState<AIResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Initialize speech recognition and synthesis
  useEffect(() => {
    console.log('Initializing AI voice system...');
    if (typeof window !== 'undefined') {
      // Speech Recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      console.log('SpeechRecognition available:', !!SpeechRecognition);
      
      if (SpeechRecognition) {
        setIsSupported(true);
        const recognition = new SpeechRecognition();
        
        recognition.continuous = options.continuous ?? false;
        recognition.interimResults = options.interimResults ?? true;
        recognition.lang = options.language ?? 'en-US';
        recognition.maxAlternatives = 1;
        
        recognitionRef.current = recognition;
        console.log('Speech recognition initialized successfully');
      } else {
        setIsSupported(false);
        setError('Speech recognition not supported in this browser');
        console.log('Speech recognition not supported');
      }

      // Speech Synthesis
      if (window.speechSynthesis && options.enableTextToSpeech) {
        synthRef.current = window.speechSynthesis;
        console.log('Speech synthesis initialized');
      }
    } else {
      console.log('Window not available (SSR)');
    }
  }, [options.continuous, options.interimResults, options.language, options.enableTextToSpeech]);

  // Process AI command
  const processAICommand = useCallback(async (userInput: string) => {
    setIsProcessing(true);
    try {
      console.log('Processing AI command:', userInput);
      const response = await aiConversationService.processCommand(userInput);
      console.log('AI response:', response);
      
      setAiResponse(response);

      // Execute the action based on AI response
      switch (response.intent) {
        case 'navigation':
          if (response.parameters?.route) {
            setLocation(response.parameters.route);
            toast({
              title: 'Navigation Command',
              description: response.response,
            });
          }
          break;
        
        case 'search':
          if (response.parameters?.query) {
            // Return the search query for the parent component to handle
            return { type: 'search', query: response.parameters.query, response };
          }
          break;
        
        case 'autoshop':
          // Return autoshop command for parent component to handle
          return { type: 'autoshop', response };
        
        case 'conversation':
          toast({
            title: 'DasWos AI',
            description: response.response,
          });
          break;
      }

      // Text-to-speech response
      if (options.enableTextToSpeech && synthRef.current && response.response) {
        speakResponse(response.response);
      }

      return { type: response.intent, response };
    } catch (error) {
      console.error('Error processing AI command:', error);
      setError('Failed to process command');
      toast({
        title: 'AI Error',
        description: 'Failed to process your command. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [setLocation, toast, options.enableTextToSpeech]);

  // Text-to-speech function
  const speakResponse = useCallback((text: string) => {
    if (synthRef.current) {
      // Cancel any ongoing speech
      synthRef.current.cancel();
      
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      utterance.volume = 0.8;
      
      // Try to use a more natural voice
      const voices = synthRef.current.getVoices();
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Google') || 
        voice.name.includes('Microsoft') ||
        voice.lang.startsWith('en')
      );
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }
      
      synthRef.current.speak(utterance);
    }
  }, []);

  // Start listening
  const startListening = useCallback(() => {
    if (!recognitionRef.current || !isSupported) {
      setError('Speech recognition not available');
      return;
    }

    try {
      setError(null);
      setTranscript('');
      setInterimTranscript('');
      setAiResponse(null);
      setIsListening(true);
      
      recognitionRef.current.start();
      
      toast({
        title: 'DasWos AI Listening',
        description: 'Speak your command...',
      });
    } catch (err) {
      setError('Failed to start voice recognition');
      setIsListening(false);
      console.error('Start listening error:', err);
    }
  }, [isSupported, toast]);

  // Stop listening
  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      setIsListening(false);
    }
  }, []);

  // Set up event listeners
  useEffect(() => {
    const recognition = recognitionRef.current;
    if (!recognition) return;

    const handleResult = async (event: SpeechRecognitionEvent) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcript = result[0].transcript;

        if (result.isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      setTranscript(finalTranscript);
      setInterimTranscript(interimTranscript);

      // Process final results with AI
      if (finalTranscript.trim()) {
        setIsListening(false);
        const result = await processAICommand(finalTranscript.trim());
        
        // Return result for parent component to handle
        if (result) {
          const event = new CustomEvent('aiVoiceCommand', {
            detail: result
          });
          window.dispatchEvent(event);
        }
      }
    };

    const handleError = (event: SpeechRecognitionErrorEvent) => {
      setError(`Speech recognition error: ${event.error}`);
      setIsListening(false);
      
      if (event.error !== 'aborted') {
        toast({
          title: 'Voice Recognition Error',
          description: `Error: ${event.error}`,
          variant: 'destructive',
        });
      }
    };

    const handleEnd = () => {
      setIsListening(false);
    };

    const handleStart = () => {
      console.log('Speech recognition started');
    };

    recognition.addEventListener('result', handleResult);
    recognition.addEventListener('error', handleError);
    recognition.addEventListener('end', handleEnd);
    recognition.addEventListener('start', handleStart);

    return () => {
      recognition.removeEventListener('result', handleResult);
      recognition.removeEventListener('error', handleError);
      recognition.removeEventListener('end', handleEnd);
      recognition.removeEventListener('start', handleStart);
    };
  }, [processAICommand, toast]);

  return {
    isListening,
    isProcessing,
    isSupported,
    transcript,
    interimTranscript,
    aiResponse,
    error,
    startListening,
    stopListening,
    speakResponse,
    processAICommand
  };
};
