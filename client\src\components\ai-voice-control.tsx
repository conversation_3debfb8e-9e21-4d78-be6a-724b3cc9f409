import React, { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume2, <PERSON>, <PERSON>ader2 } from 'lucide-react';
import { useAIVoice } from '@/hooks/use-ai-voice';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface AIVoiceControlProps {
  isAiModeEnabled: boolean;
  onSearchCommand: (query: string) => void;
  onAutoShopCommand: () => void;
  className?: string;
  enableTextToSpeech?: boolean;
}

const AIVoiceControl: React.FC<AIVoiceControlProps> = ({
  isAiModeEnabled,
  onSearchCommand,
  onAutoShopCommand,
  className,
  enableTextToSpeech = true
}) => {
  const {
    isListening,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    interimTranscript,
    aiResponse,
    error,
    startListening,
    stopListening,
    checkMicrophonePermission
  } = useAIVoice({
    continuous: false,
    interimResults: true,
    language: 'en-US',
    enableTextToSpeech
  });

  // Listen for AI voice commands
  useEffect(() => {
    const handleAIVoiceCommand = (event: CustomEvent) => {
      const { type, query, response } = event.detail;

      switch (type) {
        case 'search':
          if (query) {
            onSearchCommand(query);
          }
          break;
        case 'autoshop':
          onAutoShopCommand();
          break;
      }
    };

    window.addEventListener('aiVoiceCommand', handleAIVoiceCommand as EventListener);
    return () => {
      window.removeEventListener('aiVoiceCommand', handleAIVoiceCommand as EventListener);
    };
  }, [onSearchCommand, onAutoShopCommand]);

  // Don't render if AI mode is disabled
  if (!isAiModeEnabled) {
    return null;
  }

  // Show disabled state if not supported
  if (!isSupported) {
    return (
      <div className={cn("relative", className)}>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          disabled={true}
          className="relative overflow-hidden opacity-50"
          aria-label="Voice recognition not supported"
          title="Voice recognition not supported in this browser"
        >
          <MicOff className="h-4 w-4 text-gray-400" />
        </Button>
      </div>
    );
  }

  // Show permission request state (both null and false)
  if (hasPermission === false || hasPermission === null) {
    const isFirstTime = hasPermission === null;
    return (
      <div className={cn("relative", className)}>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={checkMicrophonePermission}
          className={cn(
            "relative overflow-hidden transition-all duration-300",
            isFirstTime
              ? "bg-gradient-to-r from-blue-500/20 via-green-500/20 to-yellow-400/20 text-blue-400 hover:text-blue-300"
              : "bg-gradient-to-r from-orange-500/20 via-red-500/20 to-pink-500/20 text-orange-400 hover:text-orange-300"
          )}
          aria-label={isFirstTime ? "Enable voice commands" : "Request microphone permission"}
          title={isFirstTime ? "Click to enable voice commands" : "Click to allow microphone access for voice commands"}
        >
          <Mic className="h-4 w-4" />
          <div className={cn(
            "absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse",
            isFirstTime ? "bg-blue-500" : "bg-orange-500"
          )}></div>
        </Button>
      </div>
    );
  }

  const handleToggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Only show the main functional button when permission is granted
  if (hasPermission !== true) {
    return null; // This should never happen due to the checks above, but just in case
  }

  return (
    <div className={cn("relative", className)}>
      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={handleToggleListening}
        className={cn(
          "relative overflow-hidden group transition-all duration-300",
          isListening
            ? "bg-gradient-to-r from-red-500/20 via-orange-500/20 to-yellow-500/20 text-red-400 hover:text-red-300"
            : isProcessing
            ? "bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-green-500/20 text-purple-400 hover:text-purple-300"
            : "bg-gradient-to-r from-blue-500/20 via-green-500/20 to-yellow-400/20 text-blue-400 hover:text-blue-300"
        )}
        disabled={!isSupported || isProcessing}
        aria-label={
          isListening
            ? "Stop voice recognition"
            : isProcessing
            ? "Processing command..."
            : "Start AI voice assistant"
        }
      >
        {/* Animated background for different states */}
        {isListening && (
          <>
            {/* Pulsing background */}
            <div className="absolute inset-0 bg-gradient-to-r from-red-400/30 via-orange-400/30 to-yellow-400/30 animate-pulse"></div>

            {/* Sound wave effect */}
            <div className="absolute inset-0 opacity-40">
              <div className="absolute top-2 left-2 w-1 h-6 bg-red-300 rounded-full animate-pulse" style={{ animationDelay: '0s' }}></div>
              <div className="absolute top-1 left-4 w-1 h-8 bg-orange-300 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="absolute top-3 left-6 w-1 h-4 bg-yellow-300 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
          </>
        )}

        {isProcessing && (
          <>
            {/* AI processing background */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-400/30 via-blue-400/30 to-green-400/30 animate-pulse"></div>

            {/* Brain wave effect */}
            <div className="absolute inset-0 opacity-40">
              <div className="absolute top-1 left-1 w-1 h-1 bg-purple-300 rounded-full animate-ping" style={{ animationDelay: '0s' }}></div>
              <div className="absolute top-2 left-3 w-1 h-1 bg-blue-300 rounded-full animate-ping" style={{ animationDelay: '0.3s' }}></div>
              <div className="absolute top-3 left-5 w-1 h-1 bg-green-300 rounded-full animate-ping" style={{ animationDelay: '0.6s' }}></div>
            </div>
          </>
        )}

        {/* Icon */}
        <div className="relative z-10">
          {isProcessing ? (
            <Brain className="h-4 w-4 animate-pulse" />
          ) : isListening ? (
            <MicOff className="h-4 w-4 animate-pulse" />
          ) : (
            <Mic className="h-4 w-4" />
          )}
        </div>

        {/* Status indicator */}
        {(isListening || isProcessing) && (
          <div className={cn(
            "absolute -top-1 -right-1 w-3 h-3 rounded-full animate-ping",
            isListening ? "bg-red-500" : "bg-purple-500"
          )}></div>
        )}
      </Button>

      {/* AI Voice feedback display */}
      {(isListening || isProcessing || interimTranscript || transcript || aiResponse) && (
        <div className="absolute top-full left-0 mt-2 p-3 bg-gray-900/95 backdrop-blur-sm border border-gray-600/50 rounded-lg shadow-lg min-w-[250px] max-w-[350px] z-50">
          {/* Status indicator */}
          <div className="flex items-center gap-2 mb-2">
            {isProcessing ? (
              <>
                <Brain className="h-3 w-3 text-purple-400 animate-pulse" />
                <span className="text-xs font-mono text-purple-300">AI PROCESSING...</span>
              </>
            ) : isListening ? (
              <>
                <Volume2 className="h-3 w-3 text-red-400" />
                <span className="text-xs font-mono text-red-300">LISTENING...</span>
              </>
            ) : (
              <>
                <Brain className="h-3 w-3 text-blue-400" />
                <span className="text-xs font-mono text-blue-300">AI READY</span>
              </>
            )}
          </div>

          {/* Transcript display */}
          {(interimTranscript || transcript) && (
            <div className="space-y-1 mb-2">
              {interimTranscript && (
                <div className="flex items-start gap-2">
                  <span className="text-xs text-gray-500 font-mono">You:</span>
                  <p className="text-xs text-gray-400 font-mono italic flex-1">
                    {interimTranscript}
                  </p>
                </div>
              )}
              {transcript && (
                <div className="flex items-start gap-2">
                  <span className="text-xs text-blue-400 font-mono">You:</span>
                  <p className="text-xs text-white font-mono flex-1">
                    "{transcript}"
                  </p>
                </div>
              )}
            </div>
          )}

          {/* AI Response */}
          {aiResponse && (
            <div className="flex items-start gap-2 mb-2 p-2 bg-blue-500/10 rounded border border-blue-500/20">
              <span className="text-xs text-green-400 font-mono">AI:</span>
              <p className="text-xs text-green-300 font-mono flex-1">
                {aiResponse.response}
              </p>
            </div>
          )}

          {/* Error display */}
          {error && (
            <p className="text-xs text-red-400 font-mono mt-2">
              Error: {error}
            </p>
          )}

          {/* Permission help */}
          {(hasPermission === false || hasPermission === null) && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-blue-400 font-mono mb-1">
                {hasPermission === null ? "Voice Commands Available:" : "Microphone Access Required:"}
              </p>
              <p className="text-xs text-gray-400 font-mono">
                {hasPermission === null
                  ? "Click the microphone button to enable voice commands and start talking to DasWos AI."
                  : "Click the microphone button to allow access and start using voice commands."
                }
              </p>
            </div>
          )}

          {/* Voice commands help */}
          {isListening && !interimTranscript && hasPermission === true && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-gray-500 font-mono mb-1">Try saying:</p>
              <ul className="text-xs text-gray-400 font-mono space-y-0.5">
                <li>• "Search for wireless headphones"</li>
                <li>• "Go to my profile"</li>
                <li>• "Start AutoShop"</li>
                <li>• "What can you do?"</li>
              </ul>
            </div>
          )}

          {/* AI capabilities info */}
          {!isListening && !isProcessing && !transcript && !aiResponse && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-gray-500 font-mono mb-1">AI Assistant Ready:</p>
              <ul className="text-xs text-gray-400 font-mono space-y-0.5">
                <li>• Natural language understanding</li>
                <li>• Smart navigation</li>
                <li>• Intelligent search</li>
                <li>• AutoShop control</li>
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AIVoiceControl;
