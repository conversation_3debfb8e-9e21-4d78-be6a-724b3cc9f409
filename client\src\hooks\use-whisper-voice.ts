import { useState, useRef, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';

interface AIResponse {
  intent: string;
  action?: string;
  parameters?: any;
  response: string;
  confidence: number;
}

interface WhisperVoiceOptions {
  enableTextToSpeech?: boolean;
}

export const useWhisperVoice = (options: WhisperVoiceOptions = {}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSupported, setIsSupported] = useState(true);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [transcript, setTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState<AIResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);

  // Use Web Speech API instead of MediaRecorder for better compatibility
  const recognitionRef = useRef<any>(null);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Check if Web Speech API is supported on mount
  useEffect(() => {
    const checkSupport = () => {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

      if (!SpeechRecognition) {
        console.log('❌ Web Speech API not supported in this browser');
        setIsSupported(false);
        setError('Speech recognition not supported in this browser');
      } else {
        console.log('✅ Web Speech API is supported');
        setIsSupported(true);
      }
    };

    checkSupport();
  }, []);

  // Check microphone permissions (simplified for Web Speech API)
  const checkMicrophonePermission = useCallback(async () => {
    console.log('🎤 Checking microphone permissions for Web Speech API...');

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.log('❌ MediaDevices API not supported');
        setHasPermission(false);
        setError('Microphone not supported in this browser');
        return false;
      }

      console.log('✅ MediaDevices API is available');

      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        console.log('✅ Microphone access granted!');
        setHasPermission(true);
        setError(null);

        // Stop the stream immediately as we only needed to check permission
        stream.getTracks().forEach(track => {
          console.log('🛑 Stopping track:', track.label);
          track.stop();
        });

        toast({
          title: 'Microphone Access Granted',
          description: 'You can now use voice commands!',
        });

        return true;
      } catch (mediaError: any) {
        console.error('❌ Microphone access denied:', mediaError);
        setHasPermission(false);

        let errorMessage = 'Microphone access denied.';
        if (mediaError.name === 'NotAllowedError') {
          errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings.';
        } else if (mediaError.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (mediaError.name === 'NotReadableError') {
          errorMessage = 'Microphone is being used by another application.';
        }

        setError(errorMessage);
        toast({
          title: 'Microphone Access Required',
          description: errorMessage,
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('💥 Error checking microphone permission:', error);
      setHasPermission(false);
      setError('Failed to check microphone permissions');
      return false;
    }
  }, [toast]);

  // Start recording using Web Speech API
  const startRecording = useCallback(async () => {
    console.log('🎯 startRecording called');

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      console.log('❌ Web Speech API not supported');
      setError('Speech recognition not supported in this browser');
      return;
    }

    console.log('✅ Web Speech API is available, starting recognition...');

    try {
      const recognition = new SpeechRecognition();
      recognitionRef.current = recognition;

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      let finalTranscript = '';
      let hasDetectedSpeech = false;

      recognition.onstart = () => {
        console.log('🎤 Speech recognition started');
        setIsRecording(true);
        setError(null);
        setTranscript('');
        setAiResponse(null);

        toast({
          title: 'DasWos AI Listening',
          description: 'Speak your command now...',
        });
      };

      recognition.onresult = (event: any) => {
        console.log('📝 Speech recognition result:', event);
        let interim = '';
        let final = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            final += transcript;
          } else {
            interim += transcript;
          }
        }

        if (interim) {
          setTranscript(`Hearing: "${interim}"`);
          hasDetectedSpeech = true;
        }

        if (final) {
          finalTranscript = final;
          setTranscript(`You said: "${final}"`);
          console.log('✅ Final transcript:', final);

          // Process with AI
          processWithAI(final);
          recognition.stop();
        }
      };

      recognition.onerror = (event: any) => {
        console.error('❌ Speech recognition error:', event.error);
        setError(`Speech recognition error: ${event.error}`);
        setIsRecording(false);

        toast({
          title: 'Speech Recognition Error',
          description: `Error: ${event.error}`,
          variant: 'destructive',
        });
      };

      recognition.onend = () => {
        console.log('🛑 Speech recognition ended');
        if (!hasDetectedSpeech && !finalTranscript) {
          setTranscript('No speech detected. Try speaking louder or closer to the microphone.');
        }
        setIsRecording(false);
      };

      recognition.onsoundstart = () => {
        console.log('🔊 Sound detected');
        setTranscript('Sound detected! Keep speaking...');
      };

      recognition.onspeechstart = () => {
        console.log('🗣️ Speech detected');
        setTranscript('Speech detected! Continue speaking...');
        hasDetectedSpeech = true;
      };

      recognition.start();

      // Auto-stop after 30 seconds
      setTimeout(() => {
        if (recognition && recognitionRef.current === recognition) {
          recognition.stop();
        }
      }, 30000);

    } catch (err: any) {
      console.error('❌ Failed to start speech recognition:', err);
      setError('Failed to start speech recognition');
      setIsRecording(false);

      toast({
        title: 'Speech Recognition Error',
        description: 'Failed to start speech recognition. Please try again.',
        variant: 'destructive',
      });
    }
  }, [toast]);

  // Stop recording
  const stopRecording = useCallback(() => {
    console.log('🛑 stopRecording called');

    if (recognitionRef.current) {
      recognitionRef.current.stop();
      console.log('🛑 Speech recognition stopped');
    }

    setIsRecording(false);
  }, []);

  // Process text with AI (using the text API instead of Whisper)
  const processWithAI = useCallback(async (userQuery: string) => {
    console.log('🤖 Processing with AI:', userQuery);
    setIsProcessing(true);

    try {
      console.log('📡 Sending text to AI API...');

      // Send to backend for processing using the text API
      const response = await fetch('/api/ai-conversation/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userQuery,
          conversationHistory: conversationHistory
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ AI response received:', data);

      const aiResponse = {
        intent: data.intent || 'conversation',
        action: data.action,
        parameters: data.parameters,
        response: data.response,
        confidence: data.confidence || 0.8
      };

      setAiResponse(aiResponse);
      setConversationHistory([...conversationHistory,
        { role: 'user', content: userQuery },
        { role: 'assistant', content: data.response }
      ]);

      // Dispatch voice command result for other components to handle
      const voiceResultEvent = new CustomEvent('voiceCommandResult', {
        detail: {
          userQuery: userQuery,
          aiResponse: aiResponse,
          audio: null // No audio from text API
        }
      });
      window.dispatchEvent(voiceResultEvent);
      console.log('📡 Dispatched voiceCommandResult event:', { userQuery, aiResponse });

      // Handle AI actions
      if (aiResponse) {
        handleAIAction(aiResponse);
      }

      // Generate and play audio response if TTS is enabled
      if (options.enableTextToSpeech && data.response) {
        try {
          console.log('🔊 Generating speech response...');
          const speechResponse = await fetch('/api/ai-conversation/tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              text: data.response
            }),
          });

          if (speechResponse.ok) {
            const speechData = await speechResponse.json();
            if (speechData.audio) {
              const audioData = `data:audio/mp3;base64,${speechData.audio}`;
              const audio = new Audio(audioData);
              await audio.play();
              console.log('🔊 Audio response played');
            }
          }
        } catch (audioError) {
          console.error('❌ Failed to generate/play audio response:', audioError);
        }
      }

      toast({
        title: 'DasWos AI Voice Command',
        description: `"${userQuery}" → ${data.response}`,
      });

    } catch (error) {
      console.error('❌ AI processing error:', error);
      setError(`Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);

      toast({
        title: 'Processing Error',
        description: 'Failed to process voice command. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [conversationHistory, options.enableTextToSpeech, toast]);

  // Handle AI actions
  const handleAIAction = useCallback((response: AIResponse) => {
    console.log('🎯 Handling AI action:', response);

    if (response.intent === 'search' && response.parameters?.query) {
      console.log('🔍 Performing search:', response.parameters.query);
      // Trigger search
      const searchEvent = new CustomEvent('aiSearch', {
        detail: { query: response.parameters.query }
      });
      window.dispatchEvent(searchEvent);
    } else if (response.intent === 'navigation' && response.parameters?.route) {
      console.log('🧭 Navigating to:', response.parameters.route);
      setLocation(response.parameters.route);
    } else if (response.intent === 'autoshop') {
      console.log('🛒 Starting AutoShop');
      const autoshopEvent = new CustomEvent('aiAutoshop');
      window.dispatchEvent(autoshopEvent);
    }
  }, [setLocation]);

  return {
    isRecording,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    aiResponse,
    error,
    startRecording,
    stopRecording,
    checkMicrophonePermission
  };
};
