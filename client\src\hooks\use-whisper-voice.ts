import { useState, useRef, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';

interface AIResponse {
  intent: string;
  action?: string;
  parameters?: any;
  response: string;
  confidence: number;
}

interface WhisperVoiceOptions {
  enableTextToSpeech?: boolean;
}

export const useWhisperVoice = (options: WhisperVoiceOptions = {}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSupported, setIsSupported] = useState(true); // MediaRecorder is widely supported
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [transcript, setTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState<AIResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Check microphone permissions
  const checkMicrophonePermission = useCallback(async () => {
    console.log('🎤 Checking microphone permissions for MediaRecorder...');
    
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.log('❌ MediaDevices API not supported');
        setHasPermission(false);
        setError('Microphone not supported in this browser');
        return false;
      }

      console.log('✅ MediaDevices API is available');

      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });
        
        console.log('✅ Microphone access granted!');
        setHasPermission(true);
        setError(null);
        
        // Stop the stream immediately as we only needed to check permission
        stream.getTracks().forEach(track => {
          console.log('🛑 Stopping track:', track.label);
          track.stop();
        });
        
        toast({
          title: 'Microphone Access Granted',
          description: 'You can now use voice commands!',
        });
        
        return true;
      } catch (mediaError: any) {
        console.error('❌ Microphone access denied:', mediaError);
        setHasPermission(false);
        
        let errorMessage = 'Microphone access denied.';
        if (mediaError.name === 'NotAllowedError') {
          errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings.';
        } else if (mediaError.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (mediaError.name === 'NotReadableError') {
          errorMessage = 'Microphone is being used by another application.';
        }
        
        setError(errorMessage);
        toast({
          title: 'Microphone Access Required',
          description: errorMessage,
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('💥 Error checking microphone permission:', error);
      setHasPermission(false);
      setError('Failed to check microphone permissions');
      return false;
    }
  }, [toast]);

  // Start recording
  const startRecording = useCallback(async () => {
    console.log('🎯 startRecording called');
    
    if (!navigator.mediaDevices || !MediaRecorder) {
      console.log('❌ MediaRecorder not supported');
      setError('Audio recording not supported in this browser');
      return;
    }

    console.log('✅ MediaRecorder is available, checking permissions...');

    // Check microphone permission first
    const hasPermission = await checkMicrophonePermission();
    if (!hasPermission) {
      console.log('❌ Microphone permission denied, cannot start recording');
      return;
    }

    try {
      console.log('🎤 Starting audio recording...');
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000 // Optimal for Whisper
        }
      });

      streamRef.current = stream;
      audioChunksRef.current = [];

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/mp4'
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
          console.log('📦 Audio chunk received:', event.data.size, 'bytes');
        }
      };

      mediaRecorder.onstop = async () => {
        console.log('🛑 MediaRecorder stopped, processing audio...');
        await processAudioWithWhisper();
      };

      mediaRecorder.onerror = (event) => {
        console.error('❌ MediaRecorder error:', event);
        setError('Recording error occurred');
        setIsRecording(false);
      };

      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);
      setError(null);
      setTranscript('');
      setAiResponse(null);

      console.log('✅ Recording started successfully');
      
      toast({
        title: 'DasWos AI Recording',
        description: 'Speak your command now...',
      });

    } catch (err) {
      console.error('❌ Failed to start recording:', err);
      setError('Failed to start recording');
      setIsRecording(false);
      
      toast({
        title: 'Recording Error',
        description: 'Failed to start recording. Please try again.',
        variant: 'destructive',
      });
    }
  }, [checkMicrophonePermission, toast]);

  // Stop recording
  const stopRecording = useCallback(() => {
    console.log('🛑 stopRecording called');
    
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      console.log('🛑 MediaRecorder stopped');
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        track.stop();
        console.log('🛑 Stopped track:', track.label);
      });
      streamRef.current = null;
    }
    
    setIsRecording(false);
  }, []);

  // Process audio with Whisper
  const processAudioWithWhisper = useCallback(async () => {
    console.log('🤖 Processing audio with Whisper...');
    setIsProcessing(true);

    try {
      if (audioChunksRef.current.length === 0) {
        throw new Error('No audio data recorded');
      }

      // Create audio blob
      const audioBlob = new Blob(audioChunksRef.current, { 
        type: mediaRecorderRef.current?.mimeType || 'audio/webm' 
      });
      
      console.log('📦 Audio blob created:', audioBlob.size, 'bytes');

      // Create form data
      const formData = new FormData();
      formData.append('audio', audioBlob, 'audio.webm');
      formData.append('conversationHistory', JSON.stringify(conversationHistory));

      console.log('📡 Sending audio to Whisper API...');

      // Send to backend for processing
      const response = await fetch('/api/ai-conversation/voice', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Whisper response received:', data);

      setTranscript(data.userQuery);
      setAiResponse(data.aiResponse);
      setConversationHistory(data.conversationHistory);

      // Handle AI actions
      if (data.aiResponse) {
        handleAIAction(data.aiResponse);
      }

      // Play audio response if available
      if (data.audio && options.enableTextToSpeech) {
        try {
          const audioData = `data:audio/mp3;base64,${data.audio}`;
          const audio = new Audio(audioData);
          await audio.play();
          console.log('🔊 Audio response played');
        } catch (audioError) {
          console.error('❌ Failed to play audio response:', audioError);
        }
      }

      toast({
        title: 'Voice Command Processed',
        description: `You said: "${data.userQuery}"`,
      });

    } catch (error) {
      console.error('❌ Whisper processing error:', error);
      setError(`Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      toast({
        title: 'Processing Error',
        description: 'Failed to process voice command. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [conversationHistory, options.enableTextToSpeech, toast]);

  // Handle AI actions
  const handleAIAction = useCallback((response: AIResponse) => {
    console.log('🎯 Handling AI action:', response);

    if (response.intent === 'search' && response.parameters?.query) {
      console.log('🔍 Performing search:', response.parameters.query);
      // Trigger search
      const searchEvent = new CustomEvent('aiSearch', {
        detail: { query: response.parameters.query }
      });
      window.dispatchEvent(searchEvent);
    } else if (response.intent === 'navigation' && response.parameters?.route) {
      console.log('🧭 Navigating to:', response.parameters.route);
      setLocation(response.parameters.route);
    } else if (response.intent === 'autoshop') {
      console.log('🛒 Starting AutoShop');
      const autoshopEvent = new CustomEvent('aiAutoshop');
      window.dispatchEvent(autoshopEvent);
    }
  }, [setLocation]);

  return {
    isRecording,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    aiResponse,
    error,
    startRecording,
    stopRecording,
    checkMicrophonePermission
  };
};
