import React, { useState, useRef, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Search, Sun, Moon, Image, X, ShoppingCart } from 'lucide-react';
import { useTheme } from '@/providers/theme-provider';
import DasWosLogo from '@/components/daswos-logo';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import AnimatedTrustText from '@/components/animated-trust-text';
import StatusLabel from '@/components/status-label';
import ShoppingResults from '@/components/shopping-results';
import InformationResults from '@/components/information-results';
import WhisperVoiceControl from '@/components/whisper-voice-control';
import { Product } from '@shared/schema';

interface SearchInterfaceProps {
  onSearch: (query: string) => void;
  aiModeEnabled?: boolean;
  onToggleAi?: (enabled: boolean) => void;
  activeSphere?: 'safesphere' | 'opensphere';
  onSphereChange?: (sphere: 'safesphere' | 'opensphere') => void;
  superSafeActive?: boolean;
  onToggleSuperSafe?: (active: boolean) => void;
  className?: string;
  showResults?: boolean;
  selectedResultType?: 'shopping' | 'information' | null;
  searchQuery?: string;
  onBuyCurrentProduct?: () => void;
  hasShoppingResults?: boolean;
  onCurrentProductChange?: (product: Product | null) => void;
}

const SearchInterface: React.FC<SearchInterfaceProps> = ({
  onSearch,
  aiModeEnabled = false,
  onToggleAi,
  activeSphere = 'safesphere',
  onSphereChange,
  superSafeActive = false,
  onToggleSuperSafe,
  className = '',
  showResults = false,
  selectedResultType = null,
  searchQuery = '',
  onBuyCurrentProduct,
  hasShoppingResults = false,
  onCurrentProductChange
}) => {
  // Reference to the container
  const containerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [currentQuery, setCurrentQuery] = useState(searchQuery);
  const [isQueryBeingEdited, setIsQueryBeingEdited] = useState(false);
  const [lastSubmittedQuery, setLastSubmittedQuery] = useState(searchQuery);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [aiResponseText, setAiResponseText] = useState<string>('');

  // Update currentQuery when searchQuery prop changes
  useEffect(() => {
    if (searchQuery && searchQuery !== currentQuery) {
      setCurrentQuery(searchQuery);
      setLastSubmittedQuery(searchQuery);
      setIsQueryBeingEdited(false);
    }
  }, [searchQuery]);

  // Detect when user is editing the query
  const handleQueryChange = (value: string) => {
    setCurrentQuery(value);

    // If AI mode is enabled and we have shopping results, check if query has changed
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      const isEditing = value.trim() !== lastSubmittedQuery.trim();
      setIsQueryBeingEdited(isEditing);

      // If user is editing, clear the current product to hide buy button
      if (isEditing && onCurrentProductChange) {
        onCurrentProductChange(null);
      }
    }
  };

  // Handle input focus - immediately switch to editing mode
  const handleInputFocus = () => {
    setIsInputFocused(true);

    // If AI mode is enabled and we have shopping results, switch to editing mode
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      setIsQueryBeingEdited(true);

      // Clear the current product to hide buy button
      if (onCurrentProductChange) {
        onCurrentProductChange(null);
      }
    }
  };

  // Handle input blur - check if we should exit editing mode
  const handleInputBlur = () => {
    setIsInputFocused(false);

    // Only exit editing mode if the query matches the last submitted query
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping') {
      const queryMatches = currentQuery.trim() === lastSubmittedQuery.trim();
      if (queryMatches) {
        setIsQueryBeingEdited(false);
        // Note: We don't restore the current product here as it will be set by the search results
      }
    }
  };

  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const [, navigate] = useLocation();

  // Voice command handlers
  const handleVoiceSearchCommand = (query: string) => {
    setCurrentQuery(query);
    onSearch(query);
    setLastSubmittedQuery(query);
    setIsQueryBeingEdited(false);

    toast({
      title: 'Voice Command Executed',
      description: `Searching for: "${query}"`,
    });
  };

  const handleVoiceAutoShopCommand = () => {
    // Trigger AutoShop settings dialog by setting the query to a known AutoShop trigger
    const autoShopQuery = "let's autoshop";
    setCurrentQuery(autoShopQuery);
    onSearch(autoShopQuery);
    setLastSubmittedQuery(autoShopQuery);
    setIsQueryBeingEdited(false);

    toast({
      title: 'Voice Command Executed',
      description: 'Opening AutoShop settings...',
    });
  };

  // Listen for AI voice events from WhisperVoiceControl
  useEffect(() => {
    const handleAISearch = (event: CustomEvent) => {
      const { query } = event.detail;
      if (query) {
        console.log('🔍 AI Search command received:', query);
        handleVoiceSearchCommand(query);
      }
    };

    const handleAIAutoshop = () => {
      console.log('🛒 AI AutoShop command received');
      handleVoiceAutoShopCommand();
    };

    // Listen for voice command results from WhisperVoiceControl
    const handleVoiceCommandResult = (event: CustomEvent) => {
      const { userQuery, aiResponse, audio } = event.detail;
      console.log('🎤 Voice command result:', { userQuery, aiResponse });

      if (userQuery) {
        // Update the search input with what the user said
        setCurrentQuery(userQuery);
        setLastSubmittedQuery(userQuery);
        setIsQueryBeingEdited(false);

        // Show AI response in search results
        if (aiResponse) {
          // Store AI response for display - ensure it's always a string
          const responseText = typeof aiResponse === 'string' ? aiResponse : (aiResponse.response || 'Processing...');
          setAiResponseText(responseText);

          // Handle different AI intents
          if (aiResponse.intent === 'search' && aiResponse.parameters?.query) {
            console.log('🔍 Executing search for:', aiResponse.parameters.query);
            // Update search query to what AI wants to search for
            setCurrentQuery(aiResponse.parameters.query);
            setLastSubmittedQuery(aiResponse.parameters.query);
            onSearch(aiResponse.parameters.query);
          } else if (aiResponse.intent === 'navigation' && aiResponse.parameters?.route) {
            console.log('🧭 Navigating to:', aiResponse.parameters.route);
            navigate(aiResponse.parameters.route);
          } else if (aiResponse.intent === 'autoshop') {
            console.log('🛒 Triggering AutoShop');
            // Dispatch AutoShop event
            const autoshopEvent = new CustomEvent('aiAutoshop');
            window.dispatchEvent(autoshopEvent);
          } else {
            // For conversation responses, show the response in the search bar
            console.log('💬 Conversation response:', responseText);
            // Don't trigger a search for conversation responses
          }

          // Show success toast with AI response
          toast({
            title: 'DasWos AI Response',
            description: responseText,
          });

          // Clear AI response after 5 seconds
          setTimeout(() => {
            setAiResponseText('');
          }, 5000);
        }
      }
    };

    window.addEventListener('aiSearch', handleAISearch as EventListener);
    window.addEventListener('aiAutoshop', handleAIAutoshop as EventListener);
    window.addEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);

    return () => {
      window.removeEventListener('aiSearch', handleAISearch as EventListener);
      window.removeEventListener('aiAutoshop', handleAIAutoshop as EventListener);
      window.removeEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
    };
  }, [navigate, onSearch, toast]);

  // Function to reset search and go to home page
  const resetSearchAndGoHome = () => {
    // Clear the search input
    setCurrentQuery('');
    // Dispatch a custom event to reset the search state in the parent component
    const resetEvent = new CustomEvent('resetSearchInterface', {
      detail: { reset: true }
    });
    window.dispatchEvent(resetEvent);
  };

  // No drag functionality - interface is fixed in position

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // If AI mode is enabled, has shopping results, not being edited, not focused, and we have a buy handler, trigger buy
    if (aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused && onBuyCurrentProduct) {
      onBuyCurrentProduct();
      return;
    }

    // Otherwise, perform normal search
    if (currentQuery.trim()) {
      onSearch(currentQuery.trim());
      setLastSubmittedQuery(currentQuery.trim());
      setIsQueryBeingEdited(false);
      // Blur the input to exit focus mode
      if (searchInputRef.current) {
        searchInputRef.current.blur();
      }
      // Don't clear the input - keep it for display in results
      // setCurrentQuery(''); // Removed to preserve search query
    }
  };



  return (
    <div
      ref={containerRef}
      className={`search-interface ${className}`}
      style={{
        position: 'relative',
        width: '932px', // Fixed width for the search bar
        margin: '0 auto',
        backgroundColor: 'transparent',
        padding: showResults ? '10px 0 0 0' : '15px 0 0 0',
        transition: 'all 0.3s ease',
        overflow: 'hidden',
        marginTop: showResults ? '-50px' : '0', // Move up when showing results
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: showResults ? 'auto' : '30vh', // Center vertically when no results
        transform: 'translateX(-40px)' // Shift left to balance the spacing
      }}
    >
      {/* No drag handle needed */}

      {/* Logo and buttons - only shown when no results */}
      {!showResults && (
        <div className="flex flex-col items-center justify-center mb-3">
          <div className="relative inline-block">
            <div className="py-1 flex justify-center">
              <div
                onClick={resetSearchAndGoHome}
                className="cursor-pointer hover:opacity-80 transition-opacity"
                title="Return to home page"
              >
                <DasWosLogo height={40} width="auto" />
              </div>
            </div>

            {/* Animated Trust Heading */}
            <div className="mt-1 mb-2 w-full text-center text-xs">
              <AnimatedTrustText
                sentences={[
                  "Helping you find what you need with confidence."
                ]}
                duration={5000}
              />
            </div>

            {/* Buttons container */}
            <div className="absolute right-[-60px] top-1/2 transform -translate-y-1/2 flex items-center space-x-3">
              {/* Theme Toggle Button */}
              <button
                onClick={toggleTheme}
                className="bg-transparent flex items-center justify-center w-8 h-8 text-xs rounded-full hover:bg-gray-100/30 dark:hover:bg-gray-800/30"
                aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
                title={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5 text-gray-400" />
                ) : (
                  <Moon className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Search form */}
      <form onSubmit={handleSubmit} className={`flex flex-col space-y-2 px-4 pb-2 ${showResults ? 'mt-0' : ''} w-full`}>
        {showResults && (
          <div className="flex items-center mb-1.5">
            <div
              onClick={resetSearchAndGoHome}
              className="cursor-pointer hover:opacity-80 transition-opacity"
              title="Return to home page"
            >
              <DasWosLogo height={30} width="auto" className="mr-3" />
            </div>
            <div className="flex-1"></div>
            {/* Theme Toggle Button when results are shown */}
            <button
              onClick={toggleTheme}
              className="bg-transparent flex items-center justify-center w-8 h-8 text-xs rounded-full hover:bg-gray-100/30 dark:hover:bg-gray-800/30"
              aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
              title={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {theme === 'dark' ? (
                <Sun className="h-5 w-5 text-gray-400" />
              ) : (
                <Moon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
        )}
        <div className="relative flex shadow-2xl rounded-lg overflow-hidden w-full max-w-[932px] mx-auto group/searchbar">
          {/* Futuristic outer glow container */}
          <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-green-500/20 to-yellow-400/20 rounded-lg blur-sm opacity-75 group-hover/searchbar:opacity-100 transition-opacity duration-300"></div>

          {/* Main search container */}
          <div className="relative flex w-full bg-gradient-to-r from-gray-900/95 via-gray-800/95 to-gray-900/95 backdrop-blur-sm border border-gray-600/50 rounded-lg overflow-hidden">
            {/* Animated background pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-transparent to-green-500/20 animate-pulse"></div>
              <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-green-400/50 to-transparent"></div>
            </div>

            {/* Scanning line effect */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-transparent via-blue-400/60 to-transparent transform -skew-x-12 animate-pulse group-hover/searchbar:animate-none group-hover/searchbar:translate-x-[932px] transition-transform duration-2000 ease-linear"></div>
            </div>

            {/* AI Mode indicator */}
            {aiModeEnabled && (
              <div className="absolute top-1 left-2 flex items-center space-x-1 z-20">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
                <div className="w-1 h-1 bg-green-400 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                <div className="w-1 h-1 bg-yellow-400 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
              </div>
            )}

            <input
              type="text"
              placeholder={
                aiModeEnabled && aiResponseText
                  ? `◉ DASWOS AI: ${aiResponseText}`
                  : aiModeEnabled
                  ? "◉ AI INTERFACE ACTIVE - SPEAK YOUR COMMAND..."
                  : "◦ ENTER SEARCH PARAMETERS..."
              }
              value={currentQuery}
              onChange={(e) => handleQueryChange(e.target.value)}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              maxLength={100}
              className={`w-full px-4 py-2 text-sm font-mono relative z-10 ${
                aiModeEnabled
                  ? 'bg-transparent text-blue-100 placeholder-blue-300/70 caret-blue-400'
                  : 'bg-transparent text-gray-100 placeholder-gray-400/70 caret-gray-300'
              } focus:outline-none focus:ring-0 border-0 rounded-l-lg h-[38px] tracking-wide`}
              ref={searchInputRef}
              style={{
                textShadow: aiModeEnabled ? '0 0 10px rgba(59, 130, 246, 0.5)' : '0 0 5px rgba(156, 163, 175, 0.3)',
                background: 'transparent'
              }}
            />
            <button
              type="submit"
              className={`px-4 search-button rounded-r-lg relative overflow-hidden group border-0 ${
                aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused
                  ? 'bg-gradient-to-r from-blue-600 via-green-500 to-yellow-400 hover:from-blue-500 hover:via-green-400 hover:to-yellow-300 text-white shadow-lg shadow-blue-500/25 hover:shadow-green-500/40 transform hover:scale-[1.02]'
                  : aiModeEnabled
                  ? 'bg-gradient-to-r from-blue-600/80 to-blue-700/80 text-blue-100 hover:from-blue-500/80 hover:to-blue-600/80'
                  : 'bg-gradient-to-r from-gray-700/80 to-gray-800/80 text-gray-200 hover:from-gray-600/80 hover:to-gray-700/80'
              } transition-all duration-300 ease-out h-[38px] flex items-center justify-center gap-1`}
              aria-label={aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused ? "◉ EXECUTE PURCHASE PROTOCOL" : aiModeEnabled ? "◉ PROCESS AI QUERY" : "◦ INITIATE SEARCH"}
            >
            {/* Futuristic animated background for buy button */}
            {aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused && (
              <>
                {/* Animated silver shimmer overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-200/30 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out"></div>

                {/* Pulsing theme color glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-green-400/20 to-yellow-400/20 animate-pulse"></div>

                {/* Theme color particle effects */}
                <div className="absolute inset-0 opacity-40">
                  <div className="absolute top-1 left-2 w-1 h-1 bg-blue-300 rounded-full animate-ping" style={{ animationDelay: '0s' }}></div>
                  <div className="absolute top-3 right-3 w-1 h-1 bg-green-300 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                  <div className="absolute bottom-2 left-4 w-1 h-1 bg-yellow-300 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
                  <div className="absolute bottom-1 right-2 w-1 h-1 bg-gray-300 rounded-full animate-ping" style={{ animationDelay: '1.5s' }}></div>
                </div>

                {/* Silver holographic border effect */}
                <div className="absolute inset-0 border border-gray-300/40 rounded-r-md animate-pulse"></div>

                {/* Additional yellow accent glow */}
                <div className="absolute inset-0 bg-gradient-to-t from-yellow-400/10 to-transparent opacity-50 group-hover:opacity-70 transition-opacity duration-300"></div>
              </>
            )}

              {/* Button content */}
              <div className="relative z-10 flex items-center gap-1">
                {aiModeEnabled && hasShoppingResults && selectedResultType === 'shopping' && !isQueryBeingEdited && !isInputFocused ? (
                  <>
                    <ShoppingCart className="h-4 w-4 drop-shadow-sm group-hover:animate-bounce text-white group-hover:text-yellow-100 transition-colors duration-300" />
                    <span className="text-xs font-bold tracking-widest drop-shadow-sm bg-gradient-to-r from-white via-gray-100 to-yellow-100 bg-clip-text text-transparent group-hover:from-yellow-100 group-hover:via-white group-hover:to-blue-100 transition-all duration-300 font-mono">
                      BUY
                    </span>
                  </>
                ) : aiModeEnabled ? (
                  <>
                    <Search className="h-4 w-4 text-blue-100" />
                    <span className="text-xs font-mono tracking-wide text-blue-100">AI</span>
                  </>
                ) : (
                  <Search className="h-4 w-4 text-gray-200" />
                )}
              </div>
            </button>

            {/* Whisper Voice Control - positioned next to search button when AI mode is enabled */}
            {aiModeEnabled && (
              <WhisperVoiceControl
                className="ml-1"
                enableTextToSpeech={true}
              />
            )}
          </div>
        </div>

        {/* Feature buttons */}
        <div className="flex justify-center mt-1.5 space-x-2 w-full max-w-[932px] mx-auto">
          {/* SafeSphere button */}
          <div
            className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${activeSphere === 'safesphere' ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
            onClick={() => onSphereChange && onSphereChange(activeSphere === 'safesphere' ? 'opensphere' : 'safesphere')}
          >
            {/* Square checkbox */}
            <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
              {activeSphere === 'safesphere' && (
                <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              )}
            </div>

            {/* Shield icon */}
            <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>

            {/* Text */}
            <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">SafeSphere</span>

            {/* Status label - only shown when active */}
            {activeSphere === 'safesphere' && (
              <span className="ml-auto text-green-500 text-[8px] font-medium w-[55px] text-right pr-1">Protected</span>
            )}
          </div>

          {/* DasWos AI button with dropdown */}
          <div className="relative">
            {/* Main button */}
            <div
              className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${aiModeEnabled ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
              onClick={() => onToggleAi && onToggleAi(!aiModeEnabled)}
            >
              {/* Square checkbox */}
              <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
                {aiModeEnabled && (
                  <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                  </svg>
                )}
              </div>

              {/* TV/Computer icon */}
              <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></rect>
                <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></line>
                <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></line>
              </svg>

              {/* Text */}
              <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">Daswos AI</span>

              {/* Status label - only shown when active */}
              {aiModeEnabled && (
                <span className="ml-auto text-blue-500 text-[8px] font-medium w-[50px] text-right pr-1">Enabled</span>
              )}
            </div>


            </div>

          {/* SuperSafe button */}
          <div
            className={`bg-white dark:bg-gray-800 rounded-sm shadow-sm border border-gray-300 dark:border-gray-600 inline-flex items-center px-2 py-1 ${superSafeActive ? 'w-[160px]' : 'w-[120px]'} cursor-pointer transition-all duration-200`}
            onClick={() => onToggleSuperSafe && onToggleSuperSafe(!superSafeActive)}
          >
            {/* Square checkbox */}
            <div className="w-4 h-4 border border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-700 flex items-center justify-center mr-2 flex-shrink-0">
              {superSafeActive && (
                <svg className="w-4 h-4 text-gray-800 dark:text-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12l5 5L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
                </svg>
              )}
            </div>

            {/* Circle check icon */}
            <svg className="h-3.5 w-3.5 mr-1.5 text-gray-700 dark:text-gray-300 flex-shrink-0" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>

            {/* Text */}
            <span className="text-gray-900 dark:text-gray-100 font-medium text-xs flex-shrink-0 whitespace-nowrap w-[70px]">SuperSafe</span>

            {/* Status label - only shown when active */}
            {superSafeActive && (
              <span className="ml-auto text-green-500 text-[8px] font-medium w-[35px] text-right pr-1">Active</span>
            )}
          </div>
        </div>


      </form>

      {/* Search Results */}
      {showResults && selectedResultType && (
        <div className="mt-4 w-full max-w-[932px] mx-auto">
          {selectedResultType === 'shopping' ? (
            <ShoppingResults
              searchQuery={searchQuery}
              sphere={activeSphere}
              className="mt-2"
              aiModeEnabled={aiModeEnabled}
              onCurrentProductChange={onCurrentProductChange}
            />
          ) : (
            <InformationResults
              searchQuery={searchQuery}
              sphere={activeSphere}
              className="mt-2"
            />
          )}
        </div>
      )}
    </div>
  );
};

export default SearchInterface;
